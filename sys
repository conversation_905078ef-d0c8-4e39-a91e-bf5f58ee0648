%!PS-Adobe-3.0
%%Creator: (ImageMagick)
%%Title: (sys)
%%CreationDate: (2025-09-28T06:53:38+00:00)
%%BoundingBox: 968 995 1072 1000
%%HiResBoundingBox: 968 995 1072 1000
%%DocumentData: Clean7Bit
%%LanguageLevel: 1
%%Orientation: Portrait
%%PageOrder: Ascend
%%Pages: 1
%%EndComments

%%BeginDefaults
%%EndDefaults

%%BeginProlog
%
% Display a color image.  The image is displayed in color on
% Postscript viewers or printers that support color, otherwise
% it is displayed as grayscale.
%
/DirectClassPacket
{
  %
  % Get a DirectClass packet.
  %
  % Parameters:
  %   red.
  %   green.
  %   blue.
  %   length: number of pixels minus one of this color (optional).
  %
  currentfile color_packet readhexstring pop pop
  compression 0 eq
  {
    /number_pixels 3 def
  }
  {
    currentfile byte readhexstring pop 0 get
    /number_pixels exch 1 add 3 mul def
  } ifelse
  0 3 number_pixels 1 sub
  {
    pixels exch color_packet putinterval
  } for
  pixels 0 number_pixels getinterval
} bind def

/DirectClassImage
{
  %
  % Display a DirectClass image.
  %
  systemdict /colorimage known
  {
    columns rows 8
    [
      columns 0 0
      rows neg 0 rows
    ]
    { DirectClassPacket } false 3 colorimage
  }
  {
    %
    % No colorimage operator;  convert to grayscale.
    %
    columns rows 8
    [
      columns 0 0
      rows neg 0 rows
    ]
    { GrayDirectClassPacket } image
  } ifelse
} bind def

/GrayDirectClassPacket
{
  %
  % Get a DirectClass packet;  convert to grayscale.
  %
  % Parameters:
  %   red
  %   green
  %   blue
  %   length: number of pixels minus one of this color (optional).
  %
  currentfile color_packet readhexstring pop pop
  color_packet 0 get 0.299 mul
  color_packet 1 get 0.587 mul add
  color_packet 2 get 0.114 mul add
  cvi
  /gray_packet exch def
  compression 0 eq
  {
    /number_pixels 1 def
  }
  {
    currentfile byte readhexstring pop 0 get
    /number_pixels exch 1 add def
  } ifelse
  0 1 number_pixels 1 sub
  {
    pixels exch gray_packet put
  } for
  pixels 0 number_pixels getinterval
} bind def

/GrayPseudoClassPacket
{
  %
  % Get a PseudoClass packet;  convert to grayscale.
  %
  % Parameters:
  %   index: index into the colormap.
  %   length: number of pixels minus one of this color (optional).
  %
  currentfile byte readhexstring pop 0 get
  /offset exch 3 mul def
  /color_packet colormap offset 3 getinterval def
  color_packet 0 get 0.299 mul
  color_packet 1 get 0.587 mul add
  color_packet 2 get 0.114 mul add
  cvi
  /gray_packet exch def
  compression 0 eq
  {
    /number_pixels 1 def
  }
  {
    currentfile byte readhexstring pop 0 get
    /number_pixels exch 1 add def
  } ifelse
  0 1 number_pixels 1 sub
  {
    pixels exch gray_packet put
  } for
  pixels 0 number_pixels getinterval
} bind def

/PseudoClassPacket
{
  %
  % Get a PseudoClass packet.
  %
  % Parameters:
  %   index: index into the colormap.
  %   length: number of pixels minus one of this color (optional).
  %
  currentfile byte readhexstring pop 0 get
  /offset exch 3 mul def
  /color_packet colormap offset 3 getinterval def
  compression 0 eq
  {
    /number_pixels 3 def
  }
  {
    currentfile byte readhexstring pop 0 get
    /number_pixels exch 1 add 3 mul def
  } ifelse
  0 3 number_pixels 1 sub
  {
    pixels exch color_packet putinterval
  } for
  pixels 0 number_pixels getinterval
} bind def

/PseudoClassImage
{
  %
  % Display a PseudoClass image.
  %
  % Parameters:
  %   class: 0-PseudoClass or 1-Grayscale.
  %
  currentfile buffer readline pop
  token pop /class exch def pop
  class 0 gt
  {
    currentfile buffer readline pop
    token pop /depth exch def pop
    /grays columns 8 add depth sub depth mul 8 idiv string def
    columns rows depth
    [
      columns 0 0
      rows neg 0 rows
    ]
    { currentfile grays readhexstring pop } image
  }
  {
    %
    % Parameters:
    %   colors: number of colors in the colormap.
    %   colormap: red, green, blue color packets.
    %
    currentfile buffer readline pop
    token pop /colors exch def pop
    /colors colors 3 mul def
    /colormap colors string def
    currentfile colormap readhexstring pop pop
    systemdict /colorimage known
    {
      columns rows 8
      [
        columns 0 0
        rows neg 0 rows
      ]
      { PseudoClassPacket } false 3 colorimage
    }
    {
      %
      % No colorimage operator;  convert to grayscale.
      %
      columns rows 8
      [
        columns 0 0
        rows neg 0 rows
      ]
      { GrayPseudoClassPacket } image
    } ifelse
  } ifelse
} bind def

/DisplayImage
{
  %
  % Display a DirectClass or PseudoClass image.
  %
  % Parameters:
  %   x & y translation.
  %   x & y scale.
  %   label pointsize.
  %   image label.
  %   image columns & rows.
  %   class: 0-DirectClass or 1-PseudoClass.
  %   compression: 0-none or 1-RunlengthEncoded.
  %   hex color packets.
  %
  gsave
  /buffer 512 string def
  /byte 1 string def
  /color_packet 3 string def
  /pixels 768 string def

  currentfile buffer readline pop
  token pop /x exch def
  token pop /y exch def pop
  x y translate
  currentfile buffer readline pop
  token pop /x exch def
  token pop /y exch def pop
  currentfile buffer readline pop
  token pop /pointsize exch def pop
  x y scale
  currentfile buffer readline pop
  token pop /columns exch def
  token pop /rows exch def pop
  currentfile buffer readline pop
  token pop /class exch def pop
  currentfile buffer readline pop
  token pop /compression exch def pop
  class 0 gt { PseudoClassImage } { DirectClassImage } ifelse
  grestore
  showpage
} bind def
%%EndProlog
%%Page:  1 1
%%PageBoundingBox: 968 995 1072 1000
DisplayImage
968 995
104 5
12
104 5
0
0
5E87B7D4ECF2F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8DA9F5E405E9FF8F8F8F8F8F8F8F8F8
C2814C405E8DC2E6F2F2E6C28D5E403B3B64A5E0F8F8F8F8DAA56446587BB1CEE6F8F8F8DABC87
58465E9FD4F8F8F8F8DAA56446587BB1CEE6F8F8F8DABC8758465E9FD4F8F8F8F8B170403B3B58
81BCE0F8F8F8CE9F6A464676B7E6F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8D48D523B3B405E87B7D4ECF2F8F8F8F8F8F8F8F8F8DAA56446587BB1CEE6F8F8F8
DABC8758465E9FD4F8F8F8F8ECB170404C76ABDAECF8F8F8DAC2A58193C2F8F8F8F8F8F8DAA564
46587BB1CEE6F8F8F8DABC8758465E9FD4F8F8F8F8D4A57B87ABC8F8F8F8F8F8F8C89364404C81
C2F2F8F8F8F8F8F8F8D48D523B3B405E87B7D4ECF2F8F8F8F8F8F8F8F8F8F8E6BC7B4C405881B7
C8F2F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F2C2814C4676B7ECF8F8F8F8F8F8F8F2BC
7B464076B7ECF8F8F8F8F8F8F2BC76463B64A5E0F8F8F8C8814C4676BCECF8F8F8F8F8F8F8F8F8
B16A405893D4F8F8C8814C4676BCECF8F8F8F8F8F8F8F8F8B16A405893D4F8F8F8B170403B6AAB
F8F8F8F8F8F8F8F2C8814C406AB1E6F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8D48D523B4C87C8F2F8F8F8F8F8F8F8F8F8F8F8F8C8814C4676BCECF8F8F8F8F8F8
F8F8F8B16A405893D4F8F8F8DA9F5E405893F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8C8814C46
76BCECF8F8F8F8F8F8F8F8F8B16A405893D4F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8E6B16A4058
93D4F8F8F8F8F8F8F8D48D523B4C87C8F2F8F8F8F8F8F8F8F8F8F8F8F8F8E0A564405287C8F2F8
F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8E6B170465287C8F2F8F8F8F8F8F8F8F8E0A5
5E40588DD4F8F8F8F8F8F8F8F8DA93584064A5E0F8F8E0A5643B3B3B3B3B3B3B3B3B3B3B3B3B3B
3B3B3B4681C2F8E0A5643B3B3B3B3B3B3B3B3B3B3B3B3B3B3B3B3B4681C2F8F8F8B170404C87C8
F8F8F8F8F8F8F8F8F8A56440528DCEF8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8D48D52406AABF8F8F8F8F8F8F8F8F8F8F8F8F8E0A5643B3B3B3B3B3B3B3B3B3B3B
3B3B3B3B3B3B4681C2F8F8F8F8DAAB6A46404C64768793A5B1C2DAF8F8F8F8F8F8F8E0A5643B3B
3B3B3B3B3B3B3B3B3B3B3B3B3B3B3B4681C2F8F8F8F2DAB78D705E4C463B3B3B3B3B3B3B3B3B52
87CEF8F8F8F8F8F8F8D48D52406AABF8F8F8F8F8F8F8F8F8F8F8F8F8F8F8C27B464070B7F8F8F8
F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8935E405EA5DAF8F8F8F8F8F8F8F8F8F8E0A5
6440588DD4F8F8F8F8F8F8F8F8DA93584064A5E0F8F8E0A564405893D4F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8F8E0A564405893D4F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8B170404C87C8
F8F8F8F8F8F8F8F8F8A56440528DCEF8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8D48D52406AB1F8F8F8F8F8F8F8F8F8F8F8F8F8E0A564405893D4F8F8F8F8F8F8F8
F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8C8B7AB9F877658463B4C76B7F8F8F8F8E0A5644058
93D4F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8935840527BB1D4E6F2F8F8F8F8ECB170404C
87CEF8F8F8F8F8F8F8D48D52406AB1F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8C27B404070B7F8F8F8
F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F2C2814C4676B7ECF8F8F8F8F8F8F8F8F8F8F8F2BC
7B464076B7ECF8F8F8F8F8F8F2BC76463B64A5E0F8F8F2C8814C406AABE6F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8F8F2C8814C406AABE6F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8B170403B6AAB
F8F8F8F8F8F8F8F2C8814C406AABE6F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8
F8F8F8F8F8F8D48D52406AB1F8F8F8F8F8F8F8F8F8F8F8F8F8F2C8814C406AABE6F8F8F8F8F8F8
F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F2BC7B464076B7F2F8F8F2C8814C40
6AABE6F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8E6AB6A40528DD4F8F8F8F8F8F8F8F8CE87523B4C
87CEF8F8F8F8F8F8F8D48D52406AB1F8F8F8F8F8F8F8F8F8F8F8F8F8F8F8E0A564405287C8F2F8

%%PageTrailer
%%Trailer
%%EOF
