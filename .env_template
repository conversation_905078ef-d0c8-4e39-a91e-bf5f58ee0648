# ===== 通用配置 =====
ENVIRONMENT=development

# ===== 代理配置 =====
# 相关搜索引擎需配置海外代理，如需可在 PROXY_URL 中添加
# PROXY_URL=
# SOCKS_PROXY=
# socks_proxy=
# HTTPS_PROXY=${PROXY_URL}
# https_proxy=${PROXY_URL}
# HTTP_PROXY=${PROXY_URL}
# http_proxy=${PROXY_URL}

# ===== MODEL =====
API_KEY=如：sk-e3fba700e89140408078debbc9fa9c0c
API_BASE_URL=https://api.deepseek.com/v1
MODEL_NAME=deepseek-chat
MAX_TOKENS=4096
TEMPERATURE=0.0
PROXY=

# ===== 工具API =====
# GOOGLE
GOOGLE_API_KEY=如：AIzaSyCm7TI7xRhGAGRaekJWSFZcjfQXqYyBx0Z
SEARCH_ENGINE_ID=如：c772a0ebd8e3a438d

# TAVILY
TAVILY_API_KEY=如：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

# Browser Use Config
HEADLESS=False
DISABLE_SECURITY=False
FORCE_KEEP_BROWSER_ALIVE=False
MINIMUM_WAIT_PAGE_LOAD_TIME=5.0
WAIT_FOR_NETWORK_IDLE_PAGE_LOAD_TIME=5.0
WAIT_BETWEEN_ACTIONS=3.0
# USER_AGENT=

# ===== MODEL 进阶配置 =====
# 可选特定 LLM 模型配置
# Co-Sight可分层配置模型：规划，执行，工具以及多模态
# 在对应的模型配置项下面，配置模型参数（API_KEY，API_BASE_URL，MODEL_NAME都配置方可生效）
# # ===== PLAN MODEL =====
# PLAN_API_KEY=
# PLAN_API_BASE_URL=
# PLAN_MODEL_NAME=
# PLAN_MAX_TOKENS=
# PLAN_TEMPERATURE=
# PLAN_PROXY=
#
# # ===== ACT MODEL =====
# ACT_API_KEY=
# ACT_API_BASE_URL=
# ACT_MODEL_NAME=
# ACT_MAX_TOKENS=
# ACT_TEMPERATURE=
# ACT_PROXY=
#
# # ===== TOOL MODEL =====
# TOOL_API_KEY=
# TOOL_API_BASE_URL=
# TOOL_MODEL_NAME=
# TOOL_MAX_TOKENS=
# TOOL_TEMPERATURE=
# TOOL_PROXY=
#
# # ===== VISION MODEL =====
# VISION_API_KEY=
# VISION_API_BASE_URL=
# VISION_MODEL_NAME=
# VISION_MAX_TOKENS=
# VISION_TEMPERATURE=
# VISION_PROXY=