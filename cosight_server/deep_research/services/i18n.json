{"zh": {"hello": "你好 {}，我是 {}！", "sys_prompt": "你的身份是中兴通讯训练的星云通信大模型(NebulaTele)。请注意星云通信大模型(NebulaTele)和星云企业大模型(NebulaBiz)都是星云大模型(NebulaLM)的子模型。对于你不了解的模型的相关问题可以回答你不了解该模型。", "thinking": "思考中...", "thinking_end": "思考完毕...", "thinking_completed": "思考完成...", "knowledge_base": "知识库", "no_description": "无描述", "built_in_knowledge_base": "内置知识库", "built_in_knowledge_base_merged_query": "内置知识库合并查询", "searching": "正在搜索...", "search_end": "搜索完毕...", "rewrite_question_start": "开始根据当前时间和上下文改写问题 ...", "rewrite_question": "理解并改写您的问题: ", "search_start": "开始在知识源 [{}] 中检索相关信息 ...", "extracting_keywords": "正在分析问题，提取搜索关键词...", "keywords_extracted": "已提取关键词: {} ({} s)", "keywords_extracted_count": "已提取 {} 个关键词 ({} s)", "analyzing_results": "正在分析搜索结果相关性，可能需要一些时间...", "reading_content": "正在读取内容...", "no_results_found": "知识库中未能找到相关信息，我将直接通过大模型回答。", "search_results_found": "已搜索到 {} 条相关内容 ({:.2f}s)", "relevant_results_found": "已筛选到 {} 条相关内容 ({:.2f}s)", "no_results_found_prefix": "抱歉，我在指定的知识源中未找到您想要的答案，我将直接通过大模型回答：\n\n--------\n\n", "no_results_found_source": "在搜索源 [{}] 中未找到相关结果", "results_found": "在搜索源 [{}] 中找到 {} 条相关结果", "references_found": "已成功读取 {} 条相关引文：\n\n{}", "concurrent_search_start": "将在 {} 个搜索源中并发搜索...", "source_search_start": "开始在搜索源 [{}] 中检索相关信息...", "all_searches_complete": "所有搜索源检索并读取完成（{:.2f}s）", "no_results_found_all_sources": "抱歉，我在所有指定的知识源中未找到相关信息，我将直接通过大模型回答：\n\n--------\n\n", "model_error": "模型响应异常，请稍后再试...", "parse_results_error": "抱歉，在解析搜索结果时发生错误，我将直接通过大模型回答：\n\n--------\n\n", "search_error": "抱歉，在搜索过程中发生错误，我将直接通过大模型回答：\n\n--------\n\n", "response_generation_error": "生成回复时发生错误。", "searching_with_time": "正在搜索 ({:.1f}s)", "search_end_with_time": "搜索完成 ({:.1f}s)", "no_search_info": "当前没有搜索过程信息", "unknown_source": "未知来源", "reference_item": "引文{}{}：[{}]({})", "reference_item_rag": "引文{}{}：[切片{}]({})", "rewrite_question_result": "已重写问题为：{} ({:.2f}s)", "retrieved_content_length": "已检索到资料的字数：{}", "start_summarizing": "开始根据检索结果进行总结，响应时间与检索到的资料字数有关，请耐心等待 ...", "ai_search_plugin_name": "AI搜索", "server_error": "服务器错误: {0}", "source_add_success": "搜索源添加成功", "source_update_success": "搜索源更新成功", "source_delete_success": "搜索源删除成功", "invalid_source_type": "搜索源类型必须是 'ZTEICenterDocument' 或 'RAGKnowledgeLibrary' 或 'Web'", "name_required": "名字不能为空", "sub_name_required": "子名字不能为空", "description_required": "描述不能为空", "owner_required": "所有者不能为空", "rag_url_required": "RAG类型搜索源必须在config中包含 url", "rag_workflow_required": "RAG类型搜索源必须在config中包含 url 和 workflow_id", "rag_credentials_required": "RAG类型搜索源必须在config中包含 app_id 和 app_key", "source_not_found": "搜索源 ID {0} 不存在", "no_permission_update": "您没有权限更新此搜索源", "no_permission_delete": "您没有权限删除此搜索源", "error_title": "错误", "source_param_missing": "未提供source参数", "reference_file_not_found": "找不到引用文件: {0}", "request_processing_error": "处理请求时出错: {0}", "invalid_command": "无法识别您的指令！请重新输入。", "welcome_title": "你好，我是智能助手小薇", "welcome_desc": "&nbsp;&nbsp;&nbsp;&nbsp;作为你的智能伙伴，我可以为你答疑解惑。想知道我还能做什么？<a onclick=\"_click('more_desc')\">点击这里</a>快速上手！", "unknown_message": "未知消息", "deep_research_wait_hint": "提示：智能体将在15至20分钟内完成相关信息的检索与报告生成，请您稍候片刻...", "deep_research_generating_outline": "====> 开始生成研究大纲...", "deep_research_not_started": "未开始", "deep_research_insight_complete": "洞察完成", "deep_research_insight_status": "====> 洞察状态", "deep_research_start_insights": "====> 开始为每个研究方面执行洞察...", "deep_research_in_progress": "正在洞察...", "deep_research_question": "## 问题：", "deep_research_answer": "答案：", "deep_research_references": "参考资料", "deep_research_start_report": "====> 开始撰写洞察报告...", "deep_research_no_insight_info": "当前没有洞察过程信息", "deep_research_searching": "正在搜索... ({time}s)", "deep_research_search_complete": "搜索完毕... ({time}s)", "deep_research_error_generating_response": "生成回复时发生错误。", "deep_research_search_query": "原始问题：{original} 当前问题：{aspect} {tags}", "deep_research_citation_format": "引文{key}URL：{url}\n", "deep_research_chat_prompt": "当前时间: {time} \n\n{prompt}", "ai_search_plugin_description": "启动AI搜索插件服务", "ai_search_port_help": "指定服务端口号", "search_source_all_space": "全空间"}, "en": {"hello": "Hello {}, I'm {}!", "sys_prompt": "Your identity is the Star Cloud Communication Model (NebulaTele) trained by ZTE. Note that the Star Cloud Communication Model (NebulaTele) and the Star Cloud Enterprise Model (NebulaBiz) are sub-models of the Star Cloud Model (NebulaLM). For questions about models you are not familiar with, you can answer that you are not familiar with the model.", "thinking": "Thinking...", "thinking_end": "End of Thinking...", "thinking_completed": "Thinking completed", "knowledge_base": "RAG", "no_description": "No Description", "built_in_knowledge_base": "Built-in RAG", "built_in_knowledge_base_merged_query": "Built-in RAG Merged Query", "searching": "Searching...", "search_end": "Search Ended...", "rewrite_question_start": "Start rewriting the question based on the current time and context ...", "rewrite_question": "Understanding and rewriting your question: ", "search_start": "Start searching in the knowledge source {} ...", "extracting_keywords": "Analyzing question, extracting search keywords...", "keywords_extracted": "Keywords extracted: {} ({} s)", "keywords_extracted_count": "Extracted {} keywords ({} s)", "analyzing_results": "Analyzing search results relevance, this may take a moment...", "reading_content": "Reading content...", "no_results_found": "No relevant information found in RAG, I will answer directly using the model.", "search_results_found": "Found {} relevant results ({:.2f}s)", "relevant_results_found": "Filtered {} relevant results ({:.2f}s)", "no_results_found_prefix": "Sorry, I couldn't find what you're looking for in the specified sources. I'll answer directly:\n\n--------\n\n", "no_results_found_source": "No results found in source [{}]", "results_found": "Found {} results in source [{}]", "references_found": "Successfully retrieved {} references:\n\n{}", "concurrent_search_start": "Starting concurrent search in {} sources...", "source_search_start": "Starting search in source [{}]...", "all_searches_complete": "All source searches completed ({:.2f}s)", "no_results_found_all_sources": "Sorry, I couldn't find relevant information in any of the specified sources. I'll answer directly:\n\n--------\n\n", "model_error": "Model response error, please try again later...", "parse_results_error": "Sorry, an error occurred while parsing search results. I'll answer directly:\n\n--------\n\n", "search_error": "Sorry, an error occurred during the search process. I'll answer directly:\n\n--------\n\n", "response_generation_error": "An error occurred while generating the response.", "searching_with_time": "Searching ({:.1f}s)", "search_end_with_time": "Search completed ({:.1f}s)", "no_search_info": "No search process information available", "unknown_source": "Unknown source", "reference_item": "Reference{}{}：[{}]({})", "reference_item_rag": "Reference{}{}：[Slice{}]({})", "rewrite_question_result": "Question rewritten as: {} ({:.2f}s)", "retrieved_content_length": "Retrieved content length: {} characters", "start_summarizing": "Starting to summarize based on search results, response time depends on content length, please wait...", "ai_search_plugin_name": "AI Search", "server_error": "Server error: {0}", "source_add_success": "Search source added successfully", "source_update_success": "Search source updated successfully", "source_delete_success": "Search source deleted successfully", "invalid_source_type": "Search source type must be 'ZTEICenterDocument' or 'RAGKnowledgeLibrary' or 'Web'", "name_required": "Name cannot be empty", "sub_name_required": "Sub name cannot be empty", "description_required": "Description cannot be empty", "owner_required": "Owner cannot be empty", "rag_url_required": "RAG type source must include url in config", "rag_workflow_required": "RAG type source must include url and workflow_id in config", "rag_credentials_required": "RAG type source must include app_id and app_key in config", "source_not_found": "Search source ID {0} does not exist", "no_permission_update": "You don't have permission to update this search source", "no_permission_delete": "You don't have permission to delete this search source", "error_title": "Error", "source_param_missing": "Source parameter is missing", "reference_file_not_found": "Reference file not found: {0}", "request_processing_error": "Error processing request: {0}", "invalid_command": "Unable to recognize your command! Please try again.", "welcome_title": "<PERSON>, I'm <PERSON>, your AI assistant", "welcome_desc": "&nbsp;&nbsp;&nbsp;&nbsp;As your intelligent partner, I can help answer your questions. Want to know what else I can do? <a onclick=\"_click('more_desc')\">Click here</a> to get started!", "unknown_message": "Unknown message", "deep_research_wait_hint": "Note: The AI agent will complete the information retrieval and report generation in 15-20 minutes, please wait...", "deep_research_generating_outline": "====> Starting to generate research outline...", "deep_research_not_started": "Not Started", "deep_research_insight_complete": "Insight Complete", "deep_research_insight_status": "====> Insight Status", "deep_research_start_insights": "====> Starting to analyze each research aspect...", "deep_research_in_progress": "Analyzing...", "deep_research_question": "## Question:", "deep_research_answer": "Answer:", "deep_research_references": "References", "deep_research_start_report": "====> Starting to write insight report...", "deep_research_no_insight_info": "No insight process information available", "deep_research_searching": "Searching... ({time}s)", "deep_research_search_complete": "Search complete... ({time}s)", "deep_research_error_generating_response": "An error occurred while generating the response.", "deep_research_search_query": "Original question: {original} Current question: {aspect} {tags}", "deep_research_citation_format": "Reference {key} URL: {url}\n", "deep_research_chat_prompt": "Current time: {time} \n\n{prompt}", "ai_search_plugin_description": "Start AI Search Plugin Service", "ai_search_port_help": "Specify service port number", "search_source_all_space": "All Space"}}