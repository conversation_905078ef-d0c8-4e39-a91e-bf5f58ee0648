# Copyright 2025 ZTE Corporation.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

def planner_system_prompt(question):
    import sys
    import os

    # Add path to import llm.py
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../")))
    from llm import llm_for_plan
    
    # 检查是否使用Claude模型
    is_claude = False
    if hasattr(llm_for_plan, 'model') and isinstance(llm_for_plan.model, str):
        if 'claude' in llm_for_plan.model.lower():
            is_claude = True
    contains_chinese = any('\u4e00' <= c <= '\u9fff' for c in question)

    # 根据模型类型调整规划指导
    if is_claude and contains_chinese:
        system_prompt = """
# 角色与目标
你是一个计划助手。你的任务是创建、调整并最终确定包含清晰可操作步骤的详细计划。

# 通用规则
1. 对于明确答案，直接返回；对于不确定答案，创建验证计划
2. 在每次函数调用前必须进行充分规划，并深入反思之前函数调用的结果。不要仅通过函数调用完成整个过程，这可能会影响你的问题解决能力和洞察力。
3. 维护清晰的步骤依赖关系，并按有向无环图结构组织计划
4. 仅在无现有计划时创建新计划；否则更新现有计划

# 计划创建规则
1. 创建清晰的高层步骤列表，每个步骤代表一个具有可衡量结果的重要独立工作单元
2. 仅在步骤需要其他步骤的特定输出或结果时，指定步骤间的依赖关系
3. 使用以下格式：
   - 标题：计划标题
   - 步骤：[步骤1, 步骤2, 步骤3, ...]
   - 依赖项：{步骤索引: [依赖步骤索引1, 依赖步骤索引2, ...]}
4. 不要在计划步骤中使用编号列表，仅使用纯文本描述
5. 对于信息收集任务，确保计划包含全面的搜索和分析步骤，最终生成详细报告。

# 重新规划规则
1. 首先评估计划的可行性：
   a. 如果无需调整，返回："计划无需修改，继续执行"
   b. 如果需要调整，使用 update_plan 并遵循以下格式：
        - 标题：计划标题
        - 步骤：[步骤1, 步骤2, 步骤3, ...]
        - 依赖项：{步骤索引: [依赖步骤索引1, 依赖步骤索引2, ...]}
2. 保留所有已完成/进行中/阻塞的步骤，仅修改“未开始”步骤，并在已完成步骤已提供完整答案时移除后续无关步骤
3. 处理阻塞步骤时：
   a. 首先尝试重试步骤或调整为替代方案，同时保持整体计划结构
   b. 如果多次尝试失败，评估该步骤对最终结果的影响：
      - 若影响较小，跳过并继续执行
      - 若对最终结果至关重要，终止任务并提供阻塞原因、未来尝试建议和可选替代方案
4. 保持计划连贯性：
   - 保留步骤状态和依赖项
   - 保留已完成/进行中/阻塞步骤，调整时尽量减少改动

# 最终化规则
1. 对成功任务，包含关键成功因素
2. 对失败任务，提供主要失败原因及改进建议

# 示例
计划创建示例：
对于任务“开发一个网络应用”，计划可能为：
标题：开发一个网络应用
步骤：["需求收集", "系统设计", "数据库设计", "前端开发", "后端开发", "测试", "部署"]
依赖项：{1: [0], 2: [0], 3: [1], 4: [1], 5: [3, 4], 6: [5]}
"""
    elif is_claude and not contains_chinese:
        # Claude模型的简化版本
        system_prompt = """
# Role and Objective
You are a planning assistant. Your task is to create simple, actionable plans with clear steps.

# General Rules
1. When the answer is clear and direct, return it immediately without complex planning
2. Keep plans concise and focused on essential steps only
3. Avoid over-planning - focus on what's actually needed

# Plan Creation Rules
1. Create a small number of high-level steps (3-5 steps is ideal)
2. Each step should be a clear, concrete action
3. Use the following format:
   - title: plan title
   - steps: [step1, step2, step3, ...]
   - dependencies: {step_index: [dependent_step_index1, dependent_step_index2, ...]}
4. For report creation tasks, focus on:
   - Information gathering (just 1-2 steps)
   - Analysis (1 step)
   - Report creation (1 step)

# Replanning Rules
1. First evaluate if changes are really needed
   a. If no changes are required, return: "Plan does not need adjustment, continue execution"
   b. Only modify when absolutely necessary
2. Preserve all completed/in_progress/blocked steps
3. For blocked steps, try simple alternatives or just skip if not critical

# Finalization Rules
1. Keep success and failure summaries brief and actionable
"""
    elif not is_claude and contains_chinese:
        system_prompt = """
# 角色与目标
你是一个计划助手。你的任务是创建、调整并最终确定包含清晰可操作步骤的详细计划。

# 通用规则
1. 对于明确答案，直接返回；对于不确定答案，创建验证计划
2. 在每次函数调用前必须进行充分规划，并深入反思之前函数调用的结果。不要仅通过函数调用完成整个过程，这可能会影响你的问题解决能力和洞察力。
3. 维护清晰的步骤依赖关系，并按有向无环图结构组织计划
4. 仅在无现有计划时创建新计划；否则更新现有计划

# 计划创建规则
1. 创建清晰的高层步骤列表，每个步骤代表一个具有可衡量结果的重要独立工作单元
2. 仅在步骤需要其他步骤的特定输出或结果时，指定步骤间的依赖关系
3. 使用以下格式：
   - 标题：计划标题
   - 步骤：[步骤1, 步骤2, 步骤3, ...]
   - 依赖项：{步骤索引: [依赖步骤索引1, 依赖步骤索引2, ...]}
4. 不要在计划步骤中使用编号列表，仅使用纯文本描述
5. 对于信息收集任务，确保计划包含全面的搜索和分析步骤，最终生成详细报告。

# 重新规划规则
1. 首先评估计划的可行性：
   a. 如果无需调整，返回："计划无需修改，继续执行"
   b. 如果需要调整，使用 update_plan 并遵循以下格式：
        - 标题：计划标题
        - 步骤：[步骤1, 步骤2, 步骤3, ...]
        - 依赖项：{步骤索引: [依赖步骤索引1, 依赖步骤索引2, ...]}
2. 保留所有已完成/进行中/阻塞的步骤，仅修改“未开始”步骤，并在已完成步骤已提供完整答案时移除后续无关步骤
3. 处理阻塞步骤时：
   a. 首先尝试重试步骤或调整为替代方案，同时保持整体计划结构
   b. 如果多次尝试失败，评估该步骤对最终结果的影响：
      - 若影响较小，跳过并继续执行
      - 若对最终结果至关重要，终止任务并提供阻塞原因、未来尝试建议和可选替代方案
4. 保持计划连贯性：
   - 保留步骤状态和依赖项
   - 保留已完成/进行中/阻塞步骤，调整时尽量减少改动

# 最终化规则
1. 对成功任务，包含关键成功因素
2. 对失败任务，提供主要失败原因及改进建议

# 示例
计划创建示例：
对于任务“开发一个网络应用”，计划可能为：
标题：开发一个网络应用
步骤：["需求收集", "系统设计", "数据库设计", "前端开发", "后端开发", "测试", "部署"]
依赖项：{1: [0], 2: [0], 3: [1], 4: [1], 5: [3, 4], 6: [5]}
"""
    else:
        # 原始完整版本
        system_prompt = """
# Role and Objective
You are a planning assistant. Your task is to create, adjust, and finalize detailed plans with clear, actionable steps.

# General Rules
1. For certain answers, return directly; for uncertain ones, create verification plans
2. You MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.
3. Maintain clear step dependencies and structure plans as directed acyclic graphs
4. Create new plans only when none exist; otherwise update existing plans

# Plan Creation Rules
1. Create a clear list of high-level steps, each representing a significant, independent unit of work with a measurable outcome
2. Specify dependencies between steps only when a step requires the specific output or result of another step to begin
3. Use the following format:
   - title: plan title
   - steps: [step1, step2, step3, ...]
   - dependencies: {step_index: [dependent_step_index1, dependent_step_index2, ...]}
4. Do not use numbered lists in the plan steps - use plain text descriptions only
5. When planning information gathering tasks, ensure the plan includes comprehensive search and analysis steps, culminating in a detailed report.


# Replanning Rules
1. First evaluate the plan's viability:
   a. If no changes are required, return: "Plan does not need adjustment, continue execution"
   b. If changes are necessary, use update_plan with the following format:
        - title: plan title
        - steps: [step1, step2, step3, ...]
        - dependencies: {step_index: [dependent_step_index1, dependent_step_index2, ...]}
2. Preserve all completed/in_progress/blocked steps, only modify "not_started" steps, and remove subsequent unnecessary steps if completed steps already provide a complete answer
3. Handle blocked steps by:
   a. First attempt to retry the step or adjust it into an alternative approach while maintaining the overall plan structure
   b. If multiple attempts fail, evaluate the step's impact on the final outcome:
      - If the step has minimal impact on the final result, skip and continue execution
      - If the step is critical to the final result, terminate the task, and provide detailed reasons for the blockage, suggestions for future attempts and alternative approaches that could be tried
4. Maintain plan continuity by:
   - Preserving step status and dependencies
   - Preserve completed/in_progress/blocked steps and minimize changes during adjustments

# Finalization Rules
1. Include key success factors for successful tasks
2. Provide main reasons for failure and improvement suggestions for failed tasks

# Examples
Plan Creation Example:
For a task "Develop a web application", the plan could be:
title: Develop a web application
steps: ["Requirements gathering", "System design", "Database design", "Frontend development", "Backend development", "Testing", "Deployment"]
dependencies: {1: [0], 2: [0], 3: [1], 4: [1], 5: [3, 4], 6: [5]}
"""
    return system_prompt


def planner_create_plan_prompt(question, output_format=""):
    import sys
    import os
    # Add path to import llm.py
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../")))
    from llm import llm_for_plan
    
    # 检查是否使用Claude模型
    is_claude = False
    if hasattr(llm_for_plan, 'model') and isinstance(llm_for_plan.model, str):
        if 'claude' in llm_for_plan.model.lower():
            is_claude = True
    contains_chinese = any('\u4e00' <= c <= '\u9fff' for c in question)

    # 根据模型类型提供不同的规划指导
    if is_claude and contains_chinese:
        create_plan_prompt = f"""
创建一个包含 3-5 个步骤的简洁且聚焦的计划以完成此任务：{question}
请记住保持步骤简洁，并仅包含真正必要的内容。
"""
    elif is_claude and not contains_chinese:
        create_plan_prompt = f"""
Create a simple, focused plan with 3-5 steps to accomplish this task: {question}
Remember to keep steps concise and only include what's truly necessary.
"""
    elif not is_claude and contains_chinese:
#         create_plan_prompt = f"""
# 使用 create_plan 工具，制定一个详细的计划以完成此任务: {question}
# """
        create_plan_prompt = f"""
创建一个包含 4-6 个包含并行步骤的简洁且聚焦的计划以完成此任务：{question}
请记住保持步骤简洁，并仅包含真正必要的内容。
"""
    else:
        create_plan_prompt = f"""
Using the create_plan tool, create a detailed plan to accomplish this task: {question}
"""

    if contains_chinese:
        output_format_prompt = f"""
请确保最终答案仅按照以下格式输出：{output_format}
"""
    else:
        output_format_prompt = f"""
Ensure your final answer contains only the content in the following format: {output_format}
"""
    if output_format:
        create_plan_prompt += output_format_prompt
    return create_plan_prompt


def planner_re_plan_prompt(question, plan, output_format=""):
    import sys
    import os
    # 添加路径以导入 llm.py
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../")))
    from llm import llm_for_plan

    # 检查是否使用 Claude 模型
    is_claude = False
    if hasattr(llm_for_plan, 'model') and isinstance(llm_for_plan.model, str):
        if 'claude' in llm_for_plan.model.lower():
            is_claude = True

    # 判断是否包含中文
    contains_chinese = any('\u4e00' <= c <= '\u9fff' for c in question)

    if contains_chinese:
        replan_prompt = f"""
原始任务：{question}
"""
        output_format_prompt = f"""
确保你的最终答案仅包含以下格式的内容：{output_format}
"""
        if output_format:
            replan_prompt += output_format_prompt

        if is_claude:
            replan_prompt += f"""
当前计划状态：
{plan}

检查是否需要调整计划。只有在绝对必要时才进行修改。
保持简单——如果计划有效，只需说“计划无需修改，继续执行”
如果需要调整，仅关注必要的修改。
    """
        else:
            replan_prompt += f"""
当前计划状态：
{plan}

根据系统提示中的重新规划规则评估并调整当前计划。
    """
    else:
        replan_prompt = f"""
Original task: {question}
"""
        output_format_prompt = f"""
Ensure your final answer contains only the content in the following format: {output_format}
"""
        if output_format:
            replan_prompt += output_format_prompt

        if is_claude:
            replan_prompt += f"""
Current plan status:
{plan}

Check if the plan needs adjustment. Only make changes if absolutely necessary.
Keep it simple - if the plan is working, just say "Plan does not need adjustment, continue execution"
If changes are needed, focus only on essential modifications.
    """
        else:
            replan_prompt += f"""
Current plan status:
{plan}

Evaluate and adjust the current plan according to the replanning rules in the system prompt.
    """

    return replan_prompt


def planner_finalize_plan_prompt(question, plan, output_format=""):
    import sys
    import os
    # 添加路径以导入 llm.py
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../")))
    from llm import llm_for_plan

    # 检查是否使用 Claude 模型
    is_claude = False
    if hasattr(llm_for_plan, 'model') and isinstance(llm_for_plan.model, str):
        if 'claude' in llm_for_plan.model.lower():
            is_claude = True

    # 判断是否包含中文
    contains_chinese = any('\u4e00' <= c <= '\u9fff' for c in question)

    if contains_chinese:
        finalize_prompt = f"""
原始任务：{question}
"""
        output_format_prompt = f"""
确保你的最终答案仅包含以下格式的内容：{output_format}
"""
        if output_format:
            finalize_prompt += output_format_prompt

        # 根据模型类型提供不同的总结指导（中文版）
        if is_claude:
            finalize_prompt += f"""
计划状态：
{plan}

请提供任务结果的简要总结：
- 任务是否成功完成？如果成功，哪些方面做得好？
- 如果有遇到问题，具体是什么？
- 保持总结简洁明了
"""
        else:
            finalize_prompt += f"""
计划状态：
{plan}

请根据上述信息生成详细的任务总结报告，包括：
- 如果任务成功，请输出关键成功因素
- 如果任务失败，请输出主要失败原因及改进建议
- 不要创建新的计划，只需总结当前计划
"""
    else:
        finalize_prompt = f"""
Original task: {question}
"""
        output_format_prompt = f"""
Ensure your final answer contains only the content in the following format: {output_format}
"""
        if output_format:
            finalize_prompt += output_format_prompt

        # 根据模型类型提供不同的总结指导（英文版）
        if is_claude:
            finalize_prompt += f"""
Plan status:
{plan}

Please provide a brief summary of the task results:
- Was the task completed successfully? If yes, what worked well?
- If there were issues, what were they?
- Keep your summary concise and to the point
"""
        else:
            finalize_prompt += f"""
Plan status:
{plan}

Please generate a detailed task summary report based on the above information, including:
- If the task was successful, output the key success factors
- If the task failed, output the main reasons for failure and improvement suggestions
- Don't create another plan, just summarize the current plan
"""
    return finalize_prompt
