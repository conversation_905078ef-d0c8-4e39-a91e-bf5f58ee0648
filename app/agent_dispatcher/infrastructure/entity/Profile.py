# Copyright 2025 ZTE Corporation.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from pydantic import BaseModel

from app.agent_dispatcher.infrastructure.entity.ProfileI18 import ProfileI18


class Profile(BaseModel):
    role: ProfileI18 | None = None
    goal: ProfileI18 | None = None
    instruction: ProfileI18 | None = None
    examples: list[ProfileI18] | None = None
    prompt: ProfileI18 | None = None

    def __init__(self, instruction: ProfileI18 | None = None, role: ProfileI18 | None = None, goal: ProfileI18 | None = None,
                 examples: list[ProfileI18] | None = None, prompt: ProfileI18 | None = None, **data):
        local = locals()
        fields = self.model_fields
        args_data = dict((k, fields.get(k).default if v is None else v) for k, v in local.items() if k in fields)
        data.update(args_data)
        super().__init__(**data)
