# Copyright 2025 ZTE Corporation.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

# 错误码开发规范
# 6位数字:
# 1. 前1位组件编号，Agent平台为7，
# 2. 后两位 组件内模块号，例如lcm，planing，message等
# 3. 最后三位是错误码范围 0-999

# i18n
I18N_NOT_EXIST = 700001

# LCM:703
AGENT_MANAGER_EXCEPTION = 703001
LCM_SERVICE_EXCEPTION = 703002
LCM_LOCAL_SERVICE_EXCEPTION = 703003
# skill：704
APIMAPPING_EXCEPTION = 704001
RAG_EXCEPTION = 704002
CHAT_EXCEPTION = 704003
LOCAL_FUNCTION_PARSE_EXCEPTION = 704004
CHAT_ASSIST_EXCEPTION = 704005
STREAM_SKILL_PROCESSING_ERROR = 704006
MCP_ERROR = 704007

# Perceive:705
UNEQUAL_QUANTITY = 705001
# Planing:706
TIME_OUT = 706001
NOT_FOUND_RECEIVER = 706002
UNAVAILABLE_LLM = 706003
PLANNING_CONFIG_ERROR = 706004
LLM_RESULT_PARSE_FAILURE = 706005
SKILL_NOT_FOUND = 706006
LONG_TERM_MEMORY_RETRIEVAL_FAILURE = 706007
PLAN_USE_RULE_NOT_SUPPORT_STREAM_ERROR = 706008
PLANNING_UNKNOWN_EXCEPTION = 706500
COMMUNICATION_UNKNOWN_EXCEPTION = 706501
COMMUNICATION_RECEIVE_EXCEPTION = 706502

# Pipe:707
PIPE_TIMEOUT = 707504
PIPE_NOT_SUPPORT_STREAM_ERROR = 707001

# Memory:708
MEMORY_RAG_FAILURE = 708001
MEMORY_MODELADAPTER_FAILURE = 708002

# UNKNOWN
TOOL_AGENT_UNKNOWN_EXCEPTION = 701001
RAG_AGENT_UNKNOWN_EXCEPTION = 701002
CHATBOT_AGENT_UNKNOWN_EXCEPTION = 701003
WORKFLOW_STEP_EXCEPTION = 701004
WORKFLOW_AGENT_UNKNOWN_EXCEPTION = 701005
MONITOR_AGENT_UNKNOWN_EXCEPTION = 701006
WORKFLOW_AGENT_NOT_SUPPORT_STREAM_ERROR = 701007
MONITOR_AGENT_NOT_SUPPORT_STREAM_ERROR = 701008

