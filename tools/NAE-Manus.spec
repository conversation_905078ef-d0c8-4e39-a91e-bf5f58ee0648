# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    [r'D:\\codes\\NAE-Manus\\manus_server\\deep_research/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        (r'D:\\codes\\NAE-Manus\\manus_server\\web', r'web'),
        (r'D:\\codes\\NAE-Manus\\manus_server\\deep_research/services/i18n.json', r'manus_server/deep_research/services'),
        (r'D:\\codes\\NAE-Manus\\app/manus/tool/deep_search/common/i18n.json', r'app/manus/tool/deep_search/common'),
        (r'D:\\codes\\NAE-Manus\\config', r'config')
    ],
    hiddenimports=[
        # 基本导入
        'uvicorn.logging', 'uvicorn.loops', 'uvicorn.loops.auto',
        'uvicorn.protocols', 'uvicorn.protocols.http', 'uvicorn.protocols.http.auto',
        'uvicorn.lifespan', 'uvicorn.lifespan.on',
        'boto3', 'botocore', 'scipy.io.matlab', 'tensorflow',
        
        # mem0相关模块
        'mem0', 'mem0.configs', 'mem0.configs.vector_stores',
        'mem0.configs.vector_stores.qdrant', 'mem0.memory', 'mem0.memory.main',
        'mem0.vector_stores.configs', 'mem0.vector_stores',
        'mem0.vector_stores.qdrant',
        
        # browser_use相关模块
        'browser_use', 'browser_use.agent', 'browser_use.agent.memory',
        'browser_use.agent.memory.service', 'browser_use.agent.service',
        
        # 向量存储相关依赖
        'qdrant_client',

        'app.agent_dispatcher',
        'app.agent_dispatcher.application',
        'app.agent_dispatcher.application.__init__',
        'app.agent_dispatcher.domain',
        'app.agent_dispatcher.domain.llm',
        'app.agent_dispatcher.domain.llm.__init__',
        'app.agent_dispatcher.domain.plan',
        'app.agent_dispatcher.domain.plan.action',
        'app.agent_dispatcher.domain.plan.action.skill',
        'app.agent_dispatcher.domain.plan.action.skill.mcp',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.const',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.engine',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.server',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.__init__',
        'app.agent_dispatcher.domain.plan.action.skill.__init__',
        'app.agent_dispatcher.domain.plan.action.__init__',
        'app.agent_dispatcher.domain.plan.__init__',
        'app.agent_dispatcher.domain.__init__',
        'app.agent_dispatcher.infrastructure',
        'app.agent_dispatcher.infrastructure.entity',
        'app.agent_dispatcher.infrastructure.entity.AgentInstance',
        'app.agent_dispatcher.infrastructure.entity.AgentTemplate',
        'app.agent_dispatcher.infrastructure.entity.ConversationHistory',
        'app.agent_dispatcher.infrastructure.entity.exception',
        'app.agent_dispatcher.infrastructure.entity.exception.error_code_consts',
        'app.agent_dispatcher.infrastructure.entity.exception.ZaeFrameworkException',
        'app.agent_dispatcher.infrastructure.entity.exception.__init__',
        'app.agent_dispatcher.infrastructure.entity.KnowledgeInfo',
        'app.agent_dispatcher.infrastructure.entity.Message',
        'app.agent_dispatcher.infrastructure.entity.MessageStream',
        'app.agent_dispatcher.infrastructure.entity.OptResult',
        'app.agent_dispatcher.infrastructure.entity.Organization',
        'app.agent_dispatcher.infrastructure.entity.Profile',
        'app.agent_dispatcher.infrastructure.entity.ProfileI18',
        'app.agent_dispatcher.infrastructure.entity.RagWorkFlow',
        'app.agent_dispatcher.infrastructure.entity.Skill',
        'app.agent_dispatcher.infrastructure.entity.SkillFunction',
        'app.agent_dispatcher.infrastructure.entity.SkillsOrchestration',
        'app.agent_dispatcher.infrastructure.entity.__init__',
        'app.agent_dispatcher.infrastructure.util',
        'app.agent_dispatcher.infrastructure.util.constants',
        'app.agent_dispatcher.infrastructure.util.__init__',
        'app.agent_dispatcher.infrastructure.__init__',
        'app.agent_dispatcher.__init__',
        'app.common',
        'app.common.application',
        'app.common.application.__init__',
        'app.common.domain',
        'app.common.domain.util',
        'app.common.domain.util.json_util',
        'app.common.domain.util.__init__',
        'app.common.domain.__init__',
        'app.common.__init__',
        'app.manus',
        'app.manus.agent',
        'app.manus.agent.actor',
        'app.manus.agent.actor.instance',
        'app.manus.agent.actor.instance.actor_agent_instance',
        'app.manus.agent.actor.instance.actor_agent_skill',
        'app.manus.agent.actor.instance.__init__',
        'app.manus.agent.actor.prompt',
        'app.manus.agent.actor.prompt.actor_prompt',
        'app.manus.agent.actor.prompt.__init__',
        'app.manus.agent.actor.task_actor_agent',
        'app.manus.agent.actor.__init__',
        'app.manus.agent.base',
        'app.manus.agent.base.base_agent',
        'app.manus.agent.base.common_skill',
        'app.manus.agent.base.skill_to_tool',
        'app.manus.agent.base.__init__',
        'app.manus.agent.planner',
        'app.manus.agent.planner.instance',
        'app.manus.agent.planner.instance.planner_agent_instance',
        'app.manus.agent.planner.instance.planner_agent_skill',
        'app.manus.agent.planner.instance.__init__',
        'app.manus.agent.planner.prompt',
        'app.manus.agent.planner.prompt.planner_prompt',
        'app.manus.agent.planner.prompt.__init__',
        'app.manus.agent.planner.task_plannr_agent',
        'app.manus.agent.planner.__init__',
        'app.manus.agent.__init__',
        'app.manus.llm',
        'app.manus.llm.chat_llm',
        'app.manus.llm.__init__',
        'app.manus.task',
        'app.manus.task.plan_report_manager',
        'app.manus.task.task_manager',
        'app.manus.task.time_record_util',
        'app.manus.task.todolist',
        'app.manus.task.__init__',
        'app.manus.tool',
        'app.manus.tool.act_toolkit',
        'app.manus.tool.arxiv_toolkit',
        'app.manus.tool.audio_toolkit',
        'app.manus.tool.code_toolkit',
        'app.manus.tool.deep_search',
        'app.manus.tool.deep_search.actions',
        'app.manus.tool.deep_search.actions.base_action',
        'app.manus.tool.deep_search.actions.web_page_reader',
        'app.manus.tool.deep_search.actions.web_search',
        'app.manus.tool.deep_search.actions.__init__',
        'app.manus.tool.deep_search.common',
        'app.manus.tool.deep_search.common.entity',
        'app.manus.tool.deep_search.common.i18n_service',
        'app.manus.tool.deep_search.common.prompts',
        'app.manus.tool.deep_search.common.utils',
        'app.manus.tool.deep_search.common.__init__',
        'app.manus.tool.deep_search.deep_search',
        'app.manus.tool.deep_search.model',
        'app.manus.tool.deep_search.model.llm_client',
        'app.manus.tool.deep_search.model.model_service',
        'app.manus.tool.deep_search.model.__init__',
        'app.manus.tool.deep_search.searchers',
        'app.manus.tool.deep_search.searchers.tavily_search',
        'app.manus.tool.deep_search.searchers.__init__',
        'app.manus.tool.deep_search.services',
        'app.manus.tool.deep_search.services.flash_search_service',
        'app.manus.tool.deep_search.services.__init__',
        'app.manus.tool.deep_search.__init__',
        'app.manus.tool.document_processing_toolkit',
        'app.manus.tool.excel_toolkit',
        'app.manus.tool.file_download_toolkit',
        'app.manus.tool.file_toolkit',
        'app.manus.tool.google_api_key',
        'app.manus.tool.google_search_util',
        'app.manus.tool.html_visualization_toolkit',
        'app.manus.tool.image_analysis_toolkit',
        'app.manus.tool.interpreters',
        'app.manus.tool.interpreters.base',
        'app.manus.tool.interpreters.internal_python_interpreter',
        'app.manus.tool.interpreters.interpreter_error',
        'app.manus.tool.interpreters.subprocess_interpreter',
        'app.manus.tool.interpreters.__init__',
        'app.manus.tool.plan_toolkit',
        'app.manus.tool.pptx_toolkit',
        'app.manus.tool.scrape_website_toolkit',
        'app.manus.tool.search_toolkit',
        'app.manus.tool.search_util',
        'app.manus.tool.shell_toolkit',
        'app.manus.tool.terminate_toolkit',
        'app.manus.tool.video_analysis_toolkit',
        'app.manus.tool.web_util',
        'app.manus.tool.__init__',
        'app.manus.__init__',
        'app.__init__',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='NAE-Manus',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r'D:\\codes\\NAE-Manus\\manus_server\\web/favicon.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='NAE-Manus',
)
