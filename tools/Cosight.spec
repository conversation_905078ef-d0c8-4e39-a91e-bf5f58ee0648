# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    [r'D:\\codes\\NAE-Manus\\cosight_server\\deep_research/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        (r'D:\\codes\\NAE-Manus\\cosight_server\\web', r'web'),
        (r'D:\\codes\\NAE-Manus\\cosight_server\\deep_research/services/i18n.json', r'cosight_server/deep_research/services'),
        (r'D:\\codes\\NAE-Manus\\app/cosight/tool/deep_search/common/i18n.json', r'app/cosight/tool/deep_search/common'),
        (r'D:\\codes\\NAE-Manus\\config', r'config')
    ],
    hiddenimports=[
        # 基本导入
        'uvicorn.logging', 'uvicorn.loops', 'uvicorn.loops.auto',
        'uvicorn.protocols', 'uvicorn.protocols.http', 'uvicorn.protocols.http.auto',
        'uvicorn.lifespan', 'uvicorn.lifespan.on',
        'boto3', 'botocore', 'scipy.io.matlab', 'tensorflow',
        
        # mem0相关模块
        'mem0', 'mem0.configs', 'mem0.configs.vector_stores',
        'mem0.configs.vector_stores.qdrant', 'mem0.memory', 'mem0.memory.main',
        'mem0.vector_stores.configs', 'mem0.vector_stores',
        'mem0.vector_stores.qdrant',
        
        # browser_use相关模块
        'browser_use', 'browser_use.agent', 'browser_use.agent.memory',
        'browser_use.agent.memory.service', 'browser_use.agent.service',
        
        # 向量存储相关依赖
        'qdrant_client',

        'app.agent_dispatcher',
        'app.agent_dispatcher.application',
        'app.agent_dispatcher.application.__init__',
        'app.agent_dispatcher.domain',
        'app.agent_dispatcher.domain.llm',
        'app.agent_dispatcher.domain.llm.__init__',
        'app.agent_dispatcher.domain.plan',
        'app.agent_dispatcher.domain.plan.action',
        'app.agent_dispatcher.domain.plan.action.skill',
        'app.agent_dispatcher.domain.plan.action.skill.mcp',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.const',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.engine',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.server',
        'app.agent_dispatcher.domain.plan.action.skill.mcp.__init__',
        'app.agent_dispatcher.domain.plan.action.skill.__init__',
        'app.agent_dispatcher.domain.plan.action.__init__',
        'app.agent_dispatcher.domain.plan.__init__',
        'app.agent_dispatcher.domain.__init__',
        'app.agent_dispatcher.infrastructure',
        'app.agent_dispatcher.infrastructure.entity',
        'app.agent_dispatcher.infrastructure.entity.AgentInstance',
        'app.agent_dispatcher.infrastructure.entity.AgentTemplate',
        'app.agent_dispatcher.infrastructure.entity.ConversationHistory',
        'app.agent_dispatcher.infrastructure.entity.exception',
        'app.agent_dispatcher.infrastructure.entity.exception.error_code_consts',
        'app.agent_dispatcher.infrastructure.entity.exception.ZaeFrameworkException',
        'app.agent_dispatcher.infrastructure.entity.exception.__init__',
        'app.agent_dispatcher.infrastructure.entity.KnowledgeInfo',
        'app.agent_dispatcher.infrastructure.entity.Message',
        'app.agent_dispatcher.infrastructure.entity.MessageStream',
        'app.agent_dispatcher.infrastructure.entity.OptResult',
        'app.agent_dispatcher.infrastructure.entity.Organization',
        'app.agent_dispatcher.infrastructure.entity.Profile',
        'app.agent_dispatcher.infrastructure.entity.ProfileI18',
        'app.agent_dispatcher.infrastructure.entity.RagWorkFlow',
        'app.agent_dispatcher.infrastructure.entity.Skill',
        'app.agent_dispatcher.infrastructure.entity.SkillFunction',
        'app.agent_dispatcher.infrastructure.entity.SkillsOrchestration',
        'app.agent_dispatcher.infrastructure.entity.__init__',
        'app.agent_dispatcher.infrastructure.util',
        'app.agent_dispatcher.infrastructure.util.constants',
        'app.agent_dispatcher.infrastructure.util.__init__',
        'app.agent_dispatcher.infrastructure.__init__',
        'app.agent_dispatcher.__init__',
        'app.common',
        'app.common.application',
        'app.common.application.__init__',
        'app.common.domain',
        'app.common.domain.util',
        'app.common.domain.util.json_util',
        'app.common.domain.util.__init__',
        'app.common.domain.__init__',
        'app.common.__init__',
        'app.cosight',
        'app.cosight.agent',
        'app.cosight.agent.actor',
        'app.cosight.agent.actor.instance',
        'app.cosight.agent.actor.instance.actor_agent_instance',
        'app.cosight.agent.actor.instance.actor_agent_skill',
        'app.cosight.agent.actor.instance.__init__',
        'app.cosight.agent.actor.prompt',
        'app.cosight.agent.actor.prompt.actor_prompt',
        'app.cosight.agent.actor.prompt.__init__',
        'app.cosight.agent.actor.task_actor_agent',
        'app.cosight.agent.actor.__init__',
        'app.cosight.agent.base',
        'app.cosight.agent.base.base_agent',
        'app.cosight.agent.base.common_skill',
        'app.cosight.agent.base.skill_to_tool',
        'app.cosight.agent.base.__init__',
        'app.cosight.agent.planner',
        'app.cosight.agent.planner.instance',
        'app.cosight.agent.planner.instance.planner_agent_instance',
        'app.cosight.agent.planner.instance.planner_agent_skill',
        'app.cosight.agent.planner.instance.__init__',
        'app.cosight.agent.planner.prompt',
        'app.cosight.agent.planner.prompt.planner_prompt',
        'app.cosight.agent.planner.prompt.__init__',
        'app.cosight.agent.planner.task_plannr_agent',
        'app.cosight.agent.planner.__init__',
        'app.cosight.agent.__init__',
        'app.cosight.llm',
        'app.cosight.llm.chat_llm',
        'app.cosight.llm.__init__',
        'app.cosight.task',
        'app.cosight.task.plan_report_manager',
        'app.cosight.task.task_manager',
        'app.cosight.task.time_record_util',
        'app.cosight.task.todolist',
        'app.cosight.task.__init__',
        'app.cosight.tool',
        'app.cosight.tool.act_toolkit',
        'app.cosight.tool.audio_toolkit',
        'app.cosight.tool.code_toolkit',
        'app.cosight.tool.deep_search',
        'app.cosight.tool.deep_search.actions',
        'app.cosight.tool.deep_search.actions.base_action',
        'app.cosight.tool.deep_search.actions.web_page_reader',
        'app.cosight.tool.deep_search.actions.web_search',
        'app.cosight.tool.deep_search.actions.__init__',
        'app.cosight.tool.deep_search.common',
        'app.cosight.tool.deep_search.common.entity',
        'app.cosight.tool.deep_search.common.i18n_service',
        'app.cosight.tool.deep_search.common.prompts',
        'app.cosight.tool.deep_search.common.utils',
        'app.cosight.tool.deep_search.common.__init__',
        'app.cosight.tool.deep_search.deep_search',
        'app.cosight.tool.deep_search.model',
        'app.cosight.tool.deep_search.model.llm_client',
        'app.cosight.tool.deep_search.model.model_service',
        'app.cosight.tool.deep_search.model.__init__',
        'app.cosight.tool.deep_search.searchers',
        'app.cosight.tool.deep_search.searchers.tavily_search',
        'app.cosight.tool.deep_search.searchers.__init__',
        'app.cosight.tool.deep_search.services',
        'app.cosight.tool.deep_search.services.flash_search_service',
        'app.cosight.tool.deep_search.services.__init__',
        'app.cosight.tool.deep_search.__init__',
        'app.cosight.tool.document_processing_toolkit',
        'app.cosight.tool.excel_toolkit',
        'app.cosight.tool.file_download_toolkit',
        'app.cosight.tool.file_toolkit',
        'app.cosight.tool.google_api_key',
        'app.cosight.tool.google_search_util',
        'app.cosight.tool.html_visualization_toolkit',
        'app.cosight.tool.image_analysis_toolkit',
        'app.cosight.tool.interpreters',
        'app.cosight.tool.interpreters.base',
        'app.cosight.tool.interpreters.internal_python_interpreter',
        'app.cosight.tool.interpreters.interpreter_error',
        'app.cosight.tool.interpreters.subprocess_interpreter',
        'app.cosight.tool.interpreters.__init__',
        'app.cosight.tool.plan_toolkit',
        'app.cosight.tool.scrape_website_toolkit',
        'app.cosight.tool.search_toolkit',
        'app.cosight.tool.search_util',
        'app.cosight.tool.shell_toolkit',
        'app.cosight.tool.terminate_toolkit',
        'app.cosight.tool.video_analysis_toolkit',
        'app.cosight.tool.web_util',
        'app.cosight.tool.__init__',
        'app.cosight.__init__',
        'app.__init__',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Cosight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r'D:\\codes\\NAE-Manus\\cosight_server\\web/favicon.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='Cosight',
)
